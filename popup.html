<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live YT Music Lyrics</title>
    <style>
        /* ========================
           CSS Custom Properties
           ======================== */
        :root {
            /* Default theme color (matches content.js) */
            --theme-color: #ff6b35;
            --theme-color-light: #ff8c66;
            --theme-color-dark: #cc5628;

            /* Alpha variants for transparency effects */
            --theme-color-alpha-10: rgba(255, 107, 53, 0.1);
            --theme-color-alpha-15: rgba(255, 107, 53, 0.15);
            --theme-color-alpha-20: rgba(255, 107, 53, 0.2);
            --theme-color-alpha-30: rgba(255, 107, 53, 0.3);
            --theme-color-alpha-40: rgba(255, 107, 53, 0.4);
            --theme-color-alpha-50: rgba(255, 107, 53, 0.5);
            --theme-color-glow: rgba(255, 107, 53, 0.7);

            /* Layout and timing constants */
            --border-radius-small: 4px;
            --border-radius-medium: 8px;
            --border-radius-large: 12px;
            --border-radius-xl: 16px;
            --transition-fast: 0.15s ease-out;
            --transition-medium: 0.3s ease-out;
            --transition-slow: 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* ========================
           Base Styles
           ======================== */
        * {
            box-sizing: border-box;
        }

        body {
            width: 380px;
            height: 520px;
            margin: 0;
            padding: 0;
            background: #030303;
            color: #ffffff;
            font-family: 'YouTube Sans', 'Roboto', 'Segoe UI', sans-serif;
            overflow: hidden;
            position: relative;
        }

        /* ========================
           Header Section
           ======================== */
        .popup-header {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            background: rgba(0, 0, 0, 0.8);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            z-index: 100;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .header-button {
            width: 32px;
            height: 32px;
            border: none;
            border-radius: var(--border-radius-medium);
            background: var(--theme-color-alpha-10);
            color: rgba(255, 255, 255, 0.7);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all var(--transition-fast);
            font-size: 14px;
        }

        .header-button:hover {
            background: var(--theme-color-alpha-20);
            color: #fff;
            transform: scale(1.05);
        }

        .header-button:active {
            transform: scale(0.95);
        }

        .master-toggle {
            position: relative;
            width: 48px;
            height: 24px;
            background: #444;
            border-radius: 12px;
            cursor: pointer;
            transition: all var(--transition-medium);
        }

        .master-toggle.active {
            background: var(--theme-color);
        }

        .master-toggle::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: all var(--transition-medium);
        }

        .master-toggle.active::after {
            transform: translateX(24px);
        }

        /* ========================
           Main Container
           ======================== */
        .popup-container {
            position: relative;
            height: 100%;
            overflow: hidden;
        }

        .popup-main {
            width: 100%;
            height: 100%;
            transition: all var(--transition-slow);
        }

        .popup-main.minimized {
            display: flex;
            flex-direction: column;
        }

        .popup-main.minimized .now-playing {
            display: none;
        }

        .popup-main.minimized .minimized-player {
            display: block;
            flex-shrink: 0;
        }

        .popup-main.minimized .lyrics-container {
            display: flex;
            flex: 1;
            position: relative;
        }

        /* ========================
           Now Playing Section (Expanded View)
           ======================== */
        .now-playing {
            padding: 24px 20px 32px 20px;
            text-align: center;
            position: relative;
        }

        .now-playing-header {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            position: relative;
        }

        .tab-nav-button {
            position: absolute;
            width: 32px;
            height: 32px;
            border: none;
            border-radius: var(--border-radius-medium);
            background: var(--theme-color-alpha-10);
            color: rgba(255, 255, 255, 0.6);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all var(--transition-fast);
            opacity: 0;
            pointer-events: none;
        }

        .tab-nav-button.visible {
            opacity: 1;
            pointer-events: auto;
        }

        .tab-nav-button:hover {
            background: var(--theme-color-alpha-20);
            color: #fff;
            transform: scale(1.1);
        }

        .tab-nav-prev {
            left: 0;
        }

        .tab-nav-next {
            right: 0;
        }

        .now-playing-title {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.6);
            font-weight: 500;
            margin: 0;
        }

        .album-cover-container {
            position: relative;
            width: 200px;
            height: 200px;
            margin: 0 auto 20px;
            border-radius: var(--border-radius-xl);
            overflow: hidden;
            background: var(--theme-color-alpha-10);
            border: 2px solid var(--theme-color-alpha-20);
        }

        .album-cover {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: all var(--transition-medium);
        }

        .album-cover-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: var(--theme-color-alpha-50);
        }

        .song-info {
            margin-bottom: 20px;
        }

        .song-title {
            font-size: 18px;
            font-weight: 600;
            color: #fff;
            margin: 0 0 8px 0;
            line-height: 1.3;
        }

        .song-artist {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
            margin: 0;
        }

        .progress-container {
            margin: 20px 0;
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
            overflow: hidden;
            margin: 8px 0;
        }

        .progress-fill {
            height: 100%;
            background: var(--theme-color);
            width: 0%;
            transition: width 0.1s linear;
        }

        .progress-time {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.5);
        }

        .playback-controls {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 16px;
            margin: 20px 0;
        }

        .control-button {
            width: 48px;
            height: 48px;
            border: none;
            border-radius: 50%;
            background: var(--theme-color-alpha-15);
            color: #fff;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all var(--transition-fast);
            font-size: 18px;
        }

        .control-button:hover {
            background: var(--theme-color-alpha-30);
            transform: scale(1.1);
        }

        .control-button.play-pause {
            width: 56px;
            height: 56px;
            background: var(--theme-color);
            font-size: 20px;
        }

        .control-button.play-pause:hover {
            background: var(--theme-color-light);
            box-shadow: 0 4px 20px var(--theme-color-alpha-50);
        }

        .control-button svg {
            width: 21px;
            /* Larger SVGs for bigger buttons */
            height: 21px;
            /* Larger SVGs for bigger buttons */
        }

        .minimize-button.control-button svg {
            width: 25px;
            /* Larger SVGs for bigger buttons */
            height: 25px;
            /* Larger SVGs for bigger buttons */
        }

        .mini-control-button.mini-expand-button svg {
            width: 22px;
            /* Larger SVGs for bigger buttons */
            height: 22px;
            /* Larger SVGs for bigger buttons */
        }

        .minimize-button {
            position: absolute;
            bottom: 5px;
            left: 50%;
            transform: translateX(-50%);
            /* width: 37px;
            height: 26px; */
            border: none;
            background: var(--theme-color-alpha-20);
            /* color: rgba(255, 255, 255, 0.6); */
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all var(--transition-fast);
            font-size: 10px;
            z-index: 10;
        }

        .minimize-button:hover {
            background: var(--theme-color-alpha-30);
            color: #fff;
            transform: translateX(-50%) scale(1.1);
        }

        /* ========================
           Minimized Player View (Flexbox Layout)
           ======================== */
        .minimized-player {
            display: none;
            /* Hidden by default */
            width: 100%;
            height: 120px;
            /* Increased height */
            background: #111;
            border-top: 1px solid var(--theme-color-alpha-20);
            position: relative;
            box-sizing: border-box;
            padding: 8px 10px 10px 10px;
            /* Increased padding */
            flex-shrink: 0;
        }

        /* New Minimized Player Layout Styles */
        .minimized-player-layout {
            display: flex;
            flex-direction: column;
            /* New: Main layout is a column */
            align-items: center;
            /* Center items horizontally within the column */
            justify-content: space-between;
            /* Distribute space vertically */
            height: 100%;
            width: 100%;
            gap: 4px;
            /* Vertical gap between song-info, media-row, progress-bar */
        }

        /* New row for album cover, playback controls, and expand button */
        .minimized-player-media-controls-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            gap: 10px;
            /* Gap between album, controls container, expand button */
        }

        .mini-album-cover {
            width: 56px;
            height: 56px;
            border-radius: var(--border-radius-medium);
            overflow: hidden;
            flex-shrink: 0;
            background-color: var(--theme-color-alpha-10);
            position: relative;
            /* For placeholder */
        }

        .mini-album-cover img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .mini-cover-placeholder {
            /* Existing styles */
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--theme-color-alpha-50);
        }

        /* .minimized-player-center-column is removed */

        .mini-song-info {
            /* Now a direct child of the column layout */
            display: flex;
            align-items: baseline;
            justify-content: center;
            width: 95%;
            /* Take most of the parent width */
            max-width: 280px;
            /* Max width for song info line */
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            text-align: center;
            /* margin-bottom will be handled by parent gap or explicit margin if needed */
        }

        .mini-song-title {
            font-size: 13px;
            /* Slightly larger for better readability on its own line */
            font-weight: 500;
            color: #fff;
            margin: 0;
            line-height: 1.2;
            display: inline;
        }

        .mini-song-title::after {
            content: "–";
            color: rgba(255, 255, 255, 0.6);
            padding: 0 5px;
            font-weight: 400;
        }

        .mini-song-artist {
            font-size: 12px;
            /* Slightly larger */
            color: rgba(255, 255, 255, 0.7);
            margin: 0;
            line-height: 1.2;
            display: inline;
            font-weight: 400;
        }

        .minimized-player-playback-controls {
            /* Now part of minimized-player-media-controls-row */
            display: flex;
            align-items: center;
            justify-content: center;
            /* Center the buttons */
            gap: 12px;
            /* Gap between actual playback buttons */
            flex-grow: 1;
            /* Allow it to take space in the middle */
            /* margin-bottom removed, parent gap controls spacing */
        }

        .mini-control-button {
            width: 34px;
            /* "Bigger" buttons */
            height: 34px;
            /* "Bigger" buttons */
            border: none;
            border-radius: 50%;
            background: var(--theme-color-alpha-15);
            color: #fff;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all var(--transition-fast);
            padding: 0;
            flex-shrink: 0;
        }

        .mini-control-button svg {
            width: 20px;
            /* Larger SVGs for bigger buttons */
            height: 20px;
            /* Larger SVGs for bigger buttons */
        }

        .mini-control-button:hover {
            background: var(--theme-color-alpha-30);
            transform: scale(1.1);
        }

        .mini-control-button.mini-play-pause {
            width: 40px;
            /* "Bigger" play/pause button */
            height: 40px;
            /* "Bigger" play/pause button */
            background: var(--theme-color);
        }

        .mini-control-button.mini-play-pause svg {
            width: 18px;
            /* Larger SVG for play/pause */
            height: 18px;
            /* Larger SVG for play/pause */
        }

        .mini-control-button.mini-play-pause:hover {
            background: var(--theme-color-light);
        }

        .mini-expand-button {
            /* Existing class, ensure it's styled as a mini-control-button */
            background: var(--theme-color-alpha-10);
            /* flex-shrink:0; already part of .mini-control-button */
        }

        .mini-expand-button:hover {
            background: var(--theme-color-alpha-20);
        }

        .mini-progress-bar {
            /* Now a direct child of the column layout */
            width: 90%;
            /* Take most of the parent width */
            max-width: none;
            /* Allow it to be wider if space permits */
            height: 6px;
            /* "Bigger" progress bar */
            margin-top: 4px;
            margin-bottom: 2px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            overflow: visible;
            position: relative;
        }

        .mini-progress-fill {
            height: 100%;
            background: var(--theme-color);
            width: 0%;
            transition: width 0.1s linear;
            border-radius: 2px;
            position: relative;
        }

        .mini-progress-fill::after {
            content: '';
            position: absolute;
            top: 50%;
            right: -4px;
            /* Adjust for slightly larger thumb */
            transform: translateY(-50%);
            width: 9px;
            /* "Bigger" thumb */
            height: 9px;
            /* "Bigger" thumb */
            background: var(--theme-color);
            border-radius: 50%;
            box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
            /* Adjusted shadow */
        }

        /* ========================
           Lyrics Container
           ======================== */
        .lyrics-container {
            display: none;
            width: 100%;
            background: linear-gradient(180deg, #030303 0%, #0a0a0a 100%);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            transition: all var(--transition-slow);
            box-shadow: inset 0 4px 20px rgba(0, 0, 0, 0.5);
        }

        .lyrics-content {
            height: 100%;
            padding: 20px 16px;
            overflow-y: auto;
            overflow-x: hidden;
            scroll-behavior: smooth;
            scrollbar-width: thin;
            scrollbar-color: rgba(120, 120, 120, 0.3) transparent;
            position: relative;
        }

        .lyrics-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 16px;
            right: 16px;
            height: 1px;
            background: linear-gradient(90deg, transparent 0%, var(--theme-color-alpha-20) 50%, transparent 100%);
        }

        .lyrics-content::-webkit-scrollbar {
            width: 3px;
        }

        .lyrics-content::-webkit-scrollbar-track {
            background: transparent;
        }

        .lyrics-content::-webkit-scrollbar-thumb {
            background: var(--theme-color-alpha-30);
            border-radius: var(--border-radius-medium);
            transition: background var(--transition-fast);
        }

        .lyrics-content::-webkit-scrollbar-thumb:hover {
            background: var(--theme-color-alpha-50);
        }

        .lyrics-loading,
        .lyrics-error {
            text-align: center;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 12px;
            height: auto;
            min-height: 0;
        }

        .lyrics-loading {
            color: var(--theme-color);
        }

        .lyrics-error {
            color: #ff4444;
        }

        .loading-icon svg,
        .error-icon svg {
            animation: spin 2s linear infinite;
        }

        .loading-text,
        .error-text {
            font-size: 14px;
            font-weight: 500;
        }

        .loading-subtext {
            font-size: 12px;
            opacity: 0.7;
        }

        @keyframes spin {
            from {
                transform: rotate(0deg);
            }

            to {
                transform: rotate(360deg);
            }
        }

        /* Lyrics lines - enhanced for popup view */
        .lyrics-line {
            padding: 10px 18px;
            margin: 8px auto;
            max-width: 95%;
            position: relative;
            font-size: 13px;
            line-height: 1.5;
            font-weight: 400;
            color: rgba(255, 255, 255, 0.5);
            letter-spacing: 0.02em;
            word-wrap: break-word;
            white-space: normal;
            text-align: center;
            border-radius: var(--border-radius-large);
            backdrop-filter: blur(8px);
            opacity: 0.8;
            cursor: pointer;
            user-select: none;
            transition: all var(--transition-fast) cubic-bezier(0.4, 0.0, 0.2, 1);
            animation: fadeInUp 0.3s ease-out;
            will-change: transform, opacity, color;
            border: 1px solid transparent;
        }

        .lyrics-line:hover {
            background: var(--theme-color-alpha-10);
            color: rgba(255, 255, 255, 0.9);
            transform: translateX(3px) scale(1.01);
            opacity: 1;
            border-color: var(--theme-color-alpha-20);
            box-shadow: 0 2px 8px var(--theme-color-alpha-15);
        }

        .lyrics-line.active {
            background: var(--theme-color-alpha-20);
            color: #fff;
            font-weight: 600;
            transform: scale(1.03) translateX(5px);
            box-shadow:
                0 6px 20px var(--theme-color-alpha-40),
                0 -2px 10px var(--theme-color-alpha-25),
                0 0 20px var(--theme-color-alpha-30);
            border: 1px solid var(--theme-color-alpha-40);
            border-radius: var(--border-radius-medium);
            font-size: 14px;
            text-shadow:
                0 0 12px var(--theme-color-glow),
                0 1px 3px rgba(0, 0, 0, 0.6);
            letter-spacing: 0.3px;
            opacity: 1;
            z-index: 10;
            position: relative;
        }

        .lyrics-line.active::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, var(--theme-color-alpha-30), var(--theme-color-alpha-50));
            border-radius: var(--border-radius-medium);
            z-index: -1;
            opacity: 0.6;
        }

        .lyrics-line.active::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, var(--theme-color-alpha-15), var(--theme-color-alpha-25));
            border-radius: var(--border-radius-medium);
            z-index: -1;
        }

        .lyrics-line.passed {
            color: rgba(255, 255, 255, 0.3);
            transform: scale(0.97);
            font-size: 12px;
            opacity: 0.6;
            font-weight: 300;
        }

        .lyrics-line.upcoming {
            color: rgba(255, 255, 255, 0.4);
            opacity: 0.7;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }

            to {
                opacity: 0.8;
                transform: translateY(0);
            }
        }

        @keyframes pulse {

            0%,
            100% {
                opacity: 1;
            }

            50% {
                opacity: 0.7;
            }
        }

        @keyframes slideInFromBottom {
            from {
                transform: translateY(100%);
                opacity: 0;
            }

            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        @keyframes glow {

            0%,
            100% {
                box-shadow: 0 0 5px var(--theme-color-alpha-30);
            }

            50% {
                box-shadow: 0 0 20px var(--theme-color-alpha-50);
            }
        }

        /* Enhanced animations for minimized state */
        .popup-main.minimized .minimized-player {
            animation: slideInFromBottom 0.4s ease-out;
        }

        .popup-main.minimized .lyrics-container {
            animation: slideInFromBottom 0.5s ease-out 0.1s both;
        }

        /* Hover effects for interactive elements */
        .mini-album-cover:hover {
            animation: glow 2s infinite;
        }

        .lyrics-line.active {
            animation: fadeInUp 0.3s ease-out, glow 3s infinite 0.5s;
        }

        /* Loading state animations */
        .loading-icon svg {
            animation: spin 1.5s linear infinite;
        }

        .lyrics-loading {
            animation: pulse 2s infinite;
        }

        /* Enhanced button animations */
        .header-button,
        .control-button,
        .mini-control-button,
        .settings-button {
            position: relative;
            overflow: hidden;
        }

        .header-button::before,
        .control-button::before,
        .mini-control-button::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.3s ease, height 0.3s ease;
        }

        .header-button:active::before,
        .control-button:active::before,
        .mini-control-button:active::before {
            width: 100%;
            height: 100%;
        }

        /* Stagger animations for lyrics lines */
        .lyrics-line:nth-child(odd) {
            animation-delay: 0.1s;
        }

        .lyrics-line:nth-child(even) {
            animation-delay: 0.2s;
        }

        /* Smooth transitions for all interactive elements */
        .album-cover-container,
        .mini-album-cover,
        .toggle-switch,
        .toggle-slider {
            transition: all var(--transition-medium) ease;
        }

        /* Hover animations for album covers */
        .album-cover-container:hover {
            transform: scale(1.02);
            box-shadow: 0 8px 30px var(--theme-color-alpha-40);
        }

        .album-cover-container:hover .album-cover {
            filter: brightness(1.1);
        }

        /* Progress bar animations */
        .progress-fill,
        .mini-progress-fill {
            background: linear-gradient(90deg, var(--theme-color) 0%, var(--theme-color-light) 100%);
            box-shadow: 0 0 10px var(--theme-color-alpha-50);
            position: relative;
        }

        .progress-fill::after {
            /* Keep original style for expanded player's progress bar */
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 4px;
            height: 100%;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 2px;
            box-shadow: 0 0 6px rgba(255, 255, 255, 0.6);
        }

        /* .mini-progress-fill::after is defined above specifically for the minimized player */

        /* Settings page entrance animation */
        .settings-page.visible .settings-section {
            animation: none;
        }

        .settings-page.visible .settings-section:nth-child(1) {
            animation-delay: 0.1s;
        }

        .settings-page.visible .settings-section:nth-child(2) {
            animation-delay: 0.2s;
        }

        .settings-page.visible .settings-section:nth-child(3) {
            animation-delay: 0.3s;
        }

        .settings-page.visible .settings-section:nth-child(4) {
            animation-delay: 0.4s;
        }

        .settings-page.visible .settings-section:nth-child(5) {
            animation-delay: 0.5s;
        }

        .settings-page.visible .settings-section:nth-child(6) {
            animation-delay: 0.6s;
        }

        @keyframes slideInFromRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }

            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Floating animation for active elements */
        @keyframes float {

            0%,
            100% {
                transform: translateY(0);
            }

            50% {
                transform: translateY(-2px);
            }
        }

        /* .control-button.play-pause:hover {
            animation: float 2s ease-in-out infinite;
        } */

        /* Ripple effect for buttons */
        @keyframes ripple {
            0% {
                transform: scale(0);
                opacity: 1;
            }

            100% {
                transform: scale(4);
                opacity: 0;
            }
        }

        .ripple-effect {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
        }

        /* Smooth state transitions */
        .popup-main,
        .minimized-player,
        .lyrics-container,
        .settings-page {
            will-change: transform, opacity;
        }

        /* Enhanced focus states */
        .header-button:focus,
        .control-button:focus,
        .mini-control-button:focus {
            outline: none;
            box-shadow: 0 0 0 3px var(--theme-color-alpha-40);
        }

        .setting-item input:focus,
        .setting-item select:focus {
            outline: none;
            box-shadow: 0 0 0 3px var(--theme-color-alpha-30);
        }

        /* Micro-interactions for toggles */
        .toggle-switch:active {
            transform: scale(0.95);
        }

        .toggle-slider {
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .toggle-switch.active .toggle-slider {
            box-shadow: 0 2px 8px var(--theme-color-alpha-50);
        }

        /* ========================
           Settings Page
           ======================== */
        .settings-page {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #030303 0%, #0a0a0a 100%);
            z-index: 200;
            opacity: 0;
            transform: translateY(100%);
            /* Initial position: below viewport */
            transition: opacity 0.3s ease-out, transform 0.3s ease-out;
            box-shadow: -4px 0 20px rgba(0, 0, 0, 0.5);
            pointer-events: none;
            /* Prevent interaction when hidden */
        }

        .settings-page.visible {
            opacity: 1;
            transform: translateY(0);
            /* Slide into view from bottom */
            pointer-events: auto;
            /* Allow interaction when visible */
        }

        /* Settings page exit animation */
        @keyframes fadeSlideIn {
            from {
                opacity: 1;
                transform: translateY(100%);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeSlideOut {
            from {
                opacity: 1;
                transform: translateY(0);
            }

            to {
                opacity: 1;
                transform: translateY(100%);
            }
        }

        .settings-page.visible {
            animation: fadeSlideIn 0.3s ease-out forwards;
        }

        .settings-page:not(.visible) {
            animation: fadeSlideOut 0.3s ease-out forwards;
        }

        /* For closing: when .visible is removed, it will transition opacity to 0 and transform to translateY(50px)
           If a different exit animation (slide top to bottom) is strictly needed, JS would be required to add a temporary class.
           The CSS-only reverse will be fade out & slide out towards bottom.
           To achieve fade out + slide out top-to-bottom (disappear upwards, then slide down on next open):
           We need to define the exit state slightly differently or rely on the natural reverse.
           Let's stick to the simple reverse for now (fade out, slide to initial Y(50px) position).
           If user meant "slide out towards the *top* edge of the screen then disappear downwards", that's more complex.
           Given "disappears/slides out from the top to bottom" likely means it moves downwards off-screen.
           The current setup (.settings-page opacity 0, transform translateY(50px)) will achieve fade out and slide down.
        */

        .settings-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 18px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            background: linear-gradient(90deg, var(--theme-color-alpha-10) 0%, var(--theme-color-alpha-20) 100%);
            backdrop-filter: blur(15px);
            position: relative;
        }

        .settings-header::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 20px;
            right: 20px;
            height: 1px;
            background: linear-gradient(90deg, transparent 0%, var(--theme-color-alpha-40) 50%, transparent 100%);
        }

        .settings-header h2 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
            color: #fff;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .settings-header h2::before {
            content: '⚙️';
            font-size: 18px;
        }

        .settings-content {
            padding: 24px 20px;
            height: calc(100% - 76px);
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: var(--theme-color-alpha-30) transparent;
        }

        .settings-content::-webkit-scrollbar {
            width: 6px;
        }

        .settings-content::-webkit-scrollbar-track {
            background: transparent;
        }

        .settings-content::-webkit-scrollbar-thumb {
            background: var(--theme-color-alpha-30);
            border-radius: 3px;
            transition: background var(--transition-fast);
        }

        .settings-content::-webkit-scrollbar-thumb:hover {
            background: var(--theme-color-alpha-50);
        }

        .settings-section {
            margin-bottom: 32px;
            background: rgba(255, 255, 255, 0.02);
            border-radius: var(--border-radius-large);
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.05);
            transition: all var(--transition-fast);
        }

        .settings-section:hover {
            background: rgba(255, 255, 255, 0.03);
            border-color: var(--theme-color-alpha-20);
        }

        .settings-section h3 {
            margin: 0 0 20px 0;
            font-size: 16px;
            font-weight: 600;
            color: var(--theme-color);
            border-bottom: 2px solid var(--theme-color-alpha-30);
            padding-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .settings-section h3::before {
            content: '';
            width: 4px;
            height: 16px;
            background: var(--theme-color);
            border-radius: 2px;
        }

        .setting-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            transition: all var(--transition-fast);
        }

        .setting-item:last-child {
            border-bottom: none;
        }

        .setting-item:hover {
            background: rgba(255, 255, 255, 0.02);
            margin: 0 -12px;
            padding: 16px 12px;
            border-radius: var(--border-radius-medium);
        }

        .setting-item label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            flex: 1;
            font-weight: 500;
            cursor: pointer;
        }

        .setting-item select {
            background: var(--theme-color-alpha-10);
            border: 1px solid var(--theme-color-alpha-20);
            border-radius: var(--border-radius-medium);
            color: #fff;
            padding: 8px 12px;
            font-size: 13px;
            min-width: 140px;
            transition: all var(--transition-fast);
        }

        .setting-item select:focus {
            outline: none;
            border-color: var(--theme-color);
            box-shadow: 0 0 0 3px var(--theme-color-alpha-20);
            background: var(--theme-color-alpha-15);
        }

        .setting-item input[type="range"] {
            flex-grow: 1;
            /* Allow slider to take available width */
            width: 100%;
            height: 8px;
            /* Slightly thicker track */
            background: var(--theme-color-alpha-30);
            /* Themed track */
            border-radius: 4px;
            outline: none;
            transition: background var(--transition-fast);
            -webkit-appearance: none;
            appearance: none;
            margin-right: 12px;
            /* Keep some space from the value span */
        }

        .setting-item input[type="range"]:focus {
            background: var(--theme-color-alpha-40);
        }

        /* Webkit (Chrome, Safari, Edge) */
        .setting-item input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            /* Larger thumb */
            height: 20px;
            /* Larger thumb */
            background: var(--theme-color);
            border-radius: 50%;
            cursor: pointer;
            border: 2px solid var(--theme-color-dark);
            /* Border for definition */
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
            transition: transform var(--transition-fast), box-shadow var(--transition-fast);
            margin-top: 0px;
        }

        .setting-item input[type="range"]::-webkit-slider-thumb:hover {
            transform: scale(1.1);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
        }

        .setting-item input[type="range"]::-webkit-slider-thumb:active {
            transform: scale(0.95);
        }

        /* Mozilla Firefox */
        .setting-item input[type="range"]::-moz-range-track {
            width: 100%;
            height: 8px;
            background: var(--theme-color-alpha-30);
            border-radius: 4px;
            border: none;
        }

        .setting-item input[type="range"]::-moz-range-thumb {
            width: 20px;
            height: 20px;
            background: var(--theme-color);
            border-radius: 50%;
            cursor: pointer;
            border: 2px solid var(--theme-color-dark);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
            transition: transform var(--transition-fast), box-shadow var(--transition-fast);
        }

        .setting-item input[type="range"]::-moz-range-thumb:hover {
            transform: scale(1.1);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
        }

        .setting-item input[type="range"]::-moz-range-thumb:active {
            transform: scale(0.95);
        }

        .setting-item span {
            font-size: 13px;
            color: var(--theme-color);
            min-width: 50px;
            text-align: right;
            font-weight: 600;
            background: var(--theme-color-alpha-10);
            padding: 4px 8px;
            border-radius: var(--border-radius-small);
        }

        .toggle-switch {
            position: relative;
            width: 50px;
            height: 26px;
            background: #444;
            border-radius: 13px;
            cursor: pointer;
            transition: all var(--transition-medium);
            border: 2px solid rgba(255, 255, 255, 0.1);
        }

        .toggle-switch.active {
            background: var(--theme-color);
            border-color: var(--theme-color-alpha-50);
            box-shadow: 0 0 10px var(--theme-color-alpha-40);
        }

        .toggle-slider {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 18px;
            height: 18px;
            background: white;
            border-radius: 50%;
            transition: all var(--transition-medium);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .toggle-switch.active .toggle-slider {
            transform: translateX(24px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }

        .current-song-info {
            background: var(--theme-color-alpha-10);
            border: 1px solid var(--theme-color-alpha-20);
            border-radius: var(--border-radius-medium);
            padding: 12px;
            margin-top: 8px;
        }

        .current-song-info .song-title {
            display: block;
            font-weight: 600;
            color: var(--theme-color);
            margin-bottom: 4px;
        }

        .current-song-info .song-artist {
            display: block;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
        }

        .settings-buttons {
            display: flex;
            flex-direction: column;
            gap: 14px;
            margin-top: 8px;
        }

        .settings-button {
            padding: 14px 18px;
            border: none;
            border-radius: var(--border-radius-medium);
            background: var(--theme-color-alpha-15);
            color: #fff;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all var(--transition-fast);
            border: 1px solid var(--theme-color-alpha-25);
            position: relative;
            overflow: hidden;
        }

        .settings-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
        }

        .settings-button:hover::before {
            left: 100%;
        }

        .settings-button:hover {
            background: var(--theme-color-alpha-30);
            border-color: var(--theme-color-alpha-50);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px var(--theme-color-alpha-30);
        }

        .settings-button:active {
            transform: translateY(0);
        }

        .settings-button.danger {
            background: rgba(255, 68, 68, 0.15);
            border-color: rgba(255, 68, 68, 0.3);
            color: #ff6b6b;
        }

        .settings-button.danger:hover {
            background: rgba(255, 68, 68, 0.25);
            border-color: rgba(255, 68, 68, 0.5);
            box-shadow: 0 4px 15px rgba(255, 68, 68, 0.3);
        }

        /* ========================
           Utility Classes
           ======================== */
        .hidden {
            display: none !important;
        }

        .visible {
            display: block !important;
        }

        /* ========================
           Loading States
           ======================== */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            backdrop-filter: blur(5px);
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid var(--theme-color-alpha-20);
            border-top: 3px solid var(--theme-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        .error-state {
            text-align: center;
            padding: 40px 20px;
            color: #ff6b6b;
        }

        .error-state svg {
            width: 48px;
            height: 48px;
            margin-bottom: 16px;
            opacity: 0.7;
        }

        .error-state h3 {
            margin: 0 0 8px 0;
            font-size: 16px;
            font-weight: 600;
        }

        .error-state p {
            margin: 0 0 16px 0;
            font-size: 14px;
            opacity: 0.8;
            line-height: 1.4;
        }

        .retry-button {
            background: var(--theme-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: var(--border-radius-medium);
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .retry-button:hover {
            background: var(--theme-color-light);
            transform: translateY(-1px);
        }

        /* ========================
           Toast Notifications
           ======================== */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 2000;
            pointer-events: none;
        }

        .toast {
            background: var(--theme-color);
            color: white;
            padding: 12px 16px;
            border-radius: var(--border-radius-medium);
            margin-bottom: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            transform: translateX(100%);
            transition: all var(--transition-medium);
            pointer-events: auto;
            font-size: 14px;
            max-width: 300px;
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast.success {
            background: #4caf50;
        }

        .toast.error {
            background: #f44336;
        }

        .toast.warning {
            background: #ff9800;
        }

        /* ========================
           Responsive Design
           ======================== */
        @media (max-width: 400px) {
            body {
                width: 320px;
                height: 480px;
            }

            .album-cover-container {
                width: 160px;
                height: 160px;
            }

            .song-title {
                font-size: 16px;
            }

            .control-button {
                width: 40px;
                height: 40px;
            }

            .control-button.play-pause {
                width: 48px;
                height: 48px;
            }

            .now-playing {
                padding: 20px 16px 28px 16px;
            }

            .settings-content {
                padding: 16px;
            }

            .setting-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .setting-item label {
                margin-bottom: 4px;
            }

            .lyrics-container {
                height: 250px;
            }

            .popup-main.minimized .lyrics-container {
                transform: translateY(-320px);
                align-items: center;
                justify-content: center;
            }
        }

        @media (max-height: 600px) {
            .lyrics-container {
                height: 250px;
            }

            .now-playing {
                padding: 16px;
            }

            .album-cover-container {
                width: 150px;
                height: 150px;
            }
        }

        /* ========================
           High DPI / Retina Support
           ======================== */
        @media (-webkit-min-device-pixel-ratio: 2),
        (min-resolution: 192dpi) {

            .album-cover,
            .mini-album-cover {
                image-rendering: -webkit-optimize-contrast;
                image-rendering: crisp-edges;
            }
        }

        /* ========================
           Accessibility Improvements
           ======================== */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* Focus indicators for keyboard navigation */
        .header-button:focus-visible,
        .control-button:focus-visible,
        .mini-control-button:focus-visible,
        .settings-button:focus-visible {
            outline: 2px solid var(--theme-color);
            outline-offset: 2px;
        }

        /* ========================
           Print Styles (for debugging)
           ======================== */
        @media print {
            body {
                background: white !important;
                color: black !important;
            }

            .popup-header,
            .playback-controls,
            .mini-controls {
                display: none !important;
            }
        }
    </style>
</head>

<body>
    <!-- Header with controls -->
    <div class="popup-header">
        <div class="header-left">
            <button class="header-button" id="settings-button" title="Settings">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path
                        d="M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97 0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1 0 .33.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z" />
                </svg>
            </button>
            <button class="header-button" id="open-tab-button" title="Open in new tab">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path
                        d="M14,3V5H17.59L7.76,14.83L9.17,16.24L19,6.41V10H21V3M19,19H5V5H12V3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V12H19V19Z" />
                </svg>
            </button>
        </div>
        <div class="header-right">
            <div class="master-toggle" id="master-toggle" title="Enable/Disable Extension"></div>
        </div>
    </div>

    <!-- Main popup container -->
    <div class="popup-container">
        <div class="popup-main" id="popup-main">
            <!-- Expanded view - Now Playing -->
            <div class="now-playing" id="now-playing">
                <div class="now-playing-header">
                    <button class="tab-nav-button tab-nav-prev" id="prev-tab" title="Previous song">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M15.41,16.58L10.83,12L15.41,7.41L14,6L8,12L14,18L15.41,16.58Z" />
                        </svg>
                    </button>
                    <h2 class="now-playing-title">Now Playing</h2>
                    <button class="tab-nav-button tab-nav-next" id="next-tab" title="Next song">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z" />
                        </svg>
                    </button>
                </div>

                <div class="album-cover-container" id="album-cover-container">
                    <img class="album-cover hidden" id="album-cover" alt="Album Cover">
                    <div class="album-cover-placeholder" id="album-cover-placeholder">
                        <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
                            <path
                                d="M12,3V13.55C11.41,13.21 10.73,13 10,13A3,3 0 0,0 7,16A3,3 0 0,0 10,19A3,3 0 0,0 13,16V7H19V5H12V3Z" />
                        </svg>
                    </div>
                </div>

                <div class="song-info">
                    <h3 class="song-title" id="song-title">No song detected</h3>
                    <p class="song-artist" id="song-artist">Open YouTube Music to start</p>
                </div>

                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill"></div>
                    </div>
                    <div class="progress-time">
                        <span id="current-time">0:00</span>
                        <span id="total-time">0:00</span>
                    </div>
                </div>

                <div class="playback-controls">
                    <button class="control-button" id="prev-button" title="Previous">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M6,18V6H8V18H6M9.5,12L18,6V18L9.5,12Z" />
                        </svg>
                    </button>
                    <button class="control-button play-pause" id="play-pause-button" title="Play/Pause">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" id="play-icon">
                            <path d="M8,5.14V19.14L19,12.14L8,5.14Z" />
                        </svg>
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" id="pause-icon"
                            class="hidden">
                            <path d="M14,19H18V5H14M6,19H10V5H6V19Z" />
                        </svg>
                    </button>
                    <button class="control-button" id="next-button" title="Next">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M16,18H18V6H16M6,18L14.5,12L6,6V18Z" />
                        </svg>
                    </button>
                </div>

                <button class="minimize-button control-button" id="minimize-button" title="Minimize">
                    <svg width="21" height="21" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z" />
                    </svg>
                </button>
            </div>

            <!-- Minimized Player View -->
            <div class="minimized-player" id="minimized-player">
                <div class="minimized-player-layout"> <!-- Main column container -->
                    <!-- Top: Song Info -->
                    <div class="mini-song-info">
                        <div class="mini-song-title" id="mini-song-title">No song detected</div>
                        <div class="mini-song-artist" id="mini-song-artist">Open YouTube Music</div>
                    </div>

                    <!-- Middle: Album Cover, Playback Controls, Expand Button -->
                    <div class="minimized-player-media-controls-row">
                        <div class="mini-album-cover">
                            <img id="mini-album-cover" alt="Album Cover" class="hidden">
                            <div class="mini-cover-placeholder" id="mini-cover-placeholder">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                    <path
                                        d="M12,3V13.55C11.41,13.21 10.73,13 10,13A3,3 0 0,0 7,16A3,3 0 0,0 10,19A3,3 0 0,0 13,16V7H19V5H12V3Z" />
                                </svg>
                            </div>
                        </div>
                        <div class="minimized-player-playback-controls">
                            <button class="mini-control-button" id="mini-prev-button" title="Previous">
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M6,18V6H8V18H6M9.5,12L18,6V18L9.5,12Z" />
                                </svg>
                            </button>
                            <button class="mini-control-button mini-play-pause" id="mini-play-pause-button"
                                title="Play/Pause">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" id="mini-play-icon">
                                    <path d="M8,5.14V19.14L19,12.14L8,5.14Z" />
                                </svg>
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" id="mini-pause-icon"
                                    class="hidden">
                                    <path d="M14,19H18V5H14M6,19H10V5H6V19Z" />
                                </svg>
                            </button>
                            <button class="mini-control-button" id="mini-next-button" title="Next">
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M16,18H18V6H16M6,18L14.5,12L6,6V18Z" />
                                </svg>
                            </button>
                        </div>
                        <button class="mini-control-button mini-expand-button" id="expand-button" title="Expand">
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M7.41,15.41L12,10.83L16.59,15.41L18,14L12,8L6,14L7.41,15.41Z" />
                            </svg>
                        </button>
                    </div>

                    <!-- Bottom: Progress Bar -->
                    <div class="mini-progress-bar">
                        <div class="mini-progress-fill" id="mini-progress-fill"></div>
                    </div>
                </div>
            </div>

            <!-- Live Lyrics Container -->
            <div class="lyrics-container" id="lyrics-container">
                <div class="lyrics-content" id="lyrics-content">
                    <div class="lyrics-loading" id="lyrics-loading">
                        <div class="loading-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12,4V2A10,10 0 0,0 2,12H4A8,8 0 0,1 12,4Z" />
                            </svg>
                        </div>
                        <div class="loading-text">Searching for lyrics...</div>
                        <div class="loading-subtext">This may take a moment</div>
                    </div>
                    <div class="lyrics-error hidden" id="lyrics-error">
                        <div class="error-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                <path
                                    d="M13,13H11V7H13M13,17H11V15H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z" />
                            </svg>
                        </div>
                        <div class="error-text" id="error-text">No lyrics found</div>
                    </div>
                    <div class="lyrics-lines" id="lyrics-lines"></div>
                </div>
            </div>
        </div>

        <!-- Settings Page -->
        <div class="settings-page hidden" id="settings-page">
            <div class="settings-header">
                <h2>Extension Settings</h2>
                <button class="header-button" id="close-settings" title="Close Settings">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path
                            d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
                    </svg>
                </button>
            </div>
            <div class="settings-content">
                <div class="settings-section">
                    <h3>Active Songs</h3>
                    <div class="setting-item">
                        <label for="active-song-selector">Configure settings for:</label>
                        <select id="active-song-selector">
                            <option value="global">Global Settings (All Songs)</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <div class="current-song-info" id="current-song-info">
                            <span class="song-title">No song selected</span>
                            <span class="song-artist">Select a song above to configure its settings</span>
                        </div>
                    </div>
                </div>

                <div class="settings-section">
                    <h3>General Settings</h3>
                    <div class="setting-item">
                        <label for="open-tab-behavior">Open in new tab behavior</label>
                        <select id="open-tab-behavior">
                            <option value="tab">Open in new tab</option>
                            <option value="popup">Show floating popup</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label for="dynamic-colors-toggle">Dynamic theme colors from album artwork</label>
                        <div class="toggle-switch" id="dynamic-colors-toggle">
                            <div class="toggle-slider"></div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label for="auto-minimize-toggle">Auto-minimize after 30 seconds</label>
                        <div class="toggle-switch" id="auto-minimize-toggle">
                            <div class="toggle-slider"></div>
                        </div>
                    </div>
                </div>

                <div class="settings-section">
                    <h3>Lyrics Display</h3>
                    <div class="setting-item">
                        <label for="font-size-slider">Font size in popup</label>
                        <input type="range" id="font-size-slider" min="10" max="20" value="13">
                        <span id="font-size-value">13px</span>
                    </div>
                    <div class="setting-item">
                        <label for="sync-offset-slider">Sync offset (fine-tune timing)</label>
                        <input type="range" id="sync-offset-slider" min="-5" max="5" step="0.1" value="0">
                        <span id="sync-offset-value">0.0s</span>
                    </div>
                    <div class="setting-item">
                        <label for="auto-scroll-toggle">Auto-scroll to current line</label>
                        <div class="toggle-switch" id="auto-scroll-toggle">
                            <div class="toggle-slider"></div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <label for="lyrics-opacity-slider">Lyrics opacity</label>
                        <input type="range" id="lyrics-opacity-slider" min="0.3" max="1" step="0.1" value="0.8">
                        <span id="lyrics-opacity-value">80%</span>
                    </div>
                    <div class="setting-item">
                        <label for="highlight-color-picker">Manual highlight color (when dynamic colors
                            disabled)</label>
                        <input type="color" id="highlight-color-picker" value="#ff0000">
                        <span id="highlight-color-value">#ff0000</span>
                    </div>
                </div>

                <div class="settings-section">
                    <h3>Keyboard Shortcuts</h3>
                    <div class="setting-item">
                        <label>Space</label>
                        <span style="background: rgba(255,255,255,0.1); color: rgba(255,255,255,0.7);">Play/Pause</span>
                    </div>
                    <div class="setting-item">
                        <label>← / →</label>
                        <span
                            style="background: rgba(255,255,255,0.1); color: rgba(255,255,255,0.7);">Previous/Next</span>
                    </div>
                    <div class="setting-item">
                        <label>Ctrl + ← / →</label>
                        <span style="background: rgba(255,255,255,0.1); color: rgba(255,255,255,0.7);">Switch
                            Tabs</span>
                    </div>
                    <div class="setting-item">
                        <label>↑ / ↓</label>
                        <span
                            style="background: rgba(255,255,255,0.1); color: rgba(255,255,255,0.7);">Expand/Minimize</span>
                    </div>
                </div>

                <div class="settings-section">
                    <h3>Data Management</h3>
                    <div class="settings-buttons">
                        <button class="settings-button" id="export-settings">
                            📤 Export Settings
                        </button>
                        <button class="settings-button" id="import-settings">
                            📥 Import Settings
                        </button>
                        <button class="settings-button" id="clear-cache">
                            🗑️ Clear Lyrics Cache
                        </button>
                        <button class="settings-button danger" id="reset-settings">
                            ⚠️ Reset All Settings
                        </button>
                    </div>
                </div>

                <div class="settings-section">
                    <h3>About</h3>
                    <div class="setting-item">
                        <label>Version</label>
                        <span style="background: rgba(255,255,255,0.1); color: rgba(255,255,255,0.7);">2.0.0</span>
                    </div>
                    <div class="setting-item">
                        <label>Developer</label>
                        <span style="background: rgba(255,255,255,0.1); color: rgba(255,255,255,0.7);">Sukarth
                            Acharya</span>
                    </div>
                    <div class="settings-buttons">
                        <button class="settings-button" id="github-link">
                            🔗 View on GitHub
                        </button>
                        <button class="settings-button" id="report-issue">
                            🐛 Report Issue
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast notification container -->
    <div class="toast-container" id="toast-container"></div>

    <!-- Loading overlay for initial load -->
    <div class="loading-overlay hidden" id="initial-loading">
        <div style="text-align: center;">
            <div class="loading-spinner"></div>
            <div style="margin-top: 16px; color: rgba(255, 255, 255, 0.8);">Initializing...</div>
        </div>
    </div>

    <script src="popup.js"></script>
</body>

</html>